# C盘空间清理工具
# 用于解决C盘空间不足问题

Write-Host "🔍 C盘空间清理工具启动..." -ForegroundColor Green
Write-Host "=" * 50

# 检查当前C盘空间
function Get-DiskSpace {
    $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $totalGB = [math]::Round($disk.Size / 1GB, 2)
    $freeGB = [math]::Round($disk.FreeSpace / 1GB, 2)
    $usedGB = [math]::Round(($disk.Size - $disk.FreeSpace) / 1GB, 2)
    $freePercent = [math]::Round(($disk.FreeSpace / $disk.Size) * 100, 2)
    
    Write-Host "💾 C盘空间状态:" -ForegroundColor Cyan
    Write-Host "  总容量: $totalGB GB" -ForegroundColor White
    Write-Host "  已使用: $usedGB GB" -ForegroundColor Yellow
    Write-Host "  可用空间: $freeGB GB ($freePercent%)" -ForegroundColor $(if($freePercent -lt 10) {"Red"} elseif($freePercent -lt 20) {"Yellow"} else {"Green"})
    Write-Host ""
    
    return @{
        TotalGB = $totalGB
        FreeGB = $freeGB
        UsedGB = $usedGB
        FreePercent = $freePercent
    }
}

# 显示初始状态
$initialSpace = Get-DiskSpace

# 清理选项菜单
function Show-CleanupMenu {
    Write-Host "🧹 请选择清理选项:" -ForegroundColor Yellow
    Write-Host "1. 清理临时文件 (推荐)" -ForegroundColor White
    Write-Host "2. 清理回收站" -ForegroundColor White
    Write-Host "3. 清理浏览器缓存" -ForegroundColor White
    Write-Host "4. 清理Windows更新缓存" -ForegroundColor White
    Write-Host "5. 清理系统日志文件" -ForegroundColor White
    Write-Host "6. 磁盘清理工具" -ForegroundColor White
    Write-Host "7. 查找大文件 (>100MB)" -ForegroundColor White
    Write-Host "8. 全部清理 (1-6)" -ForegroundColor Green
    Write-Host "9. 分析磁盘使用情况" -ForegroundColor Cyan
    Write-Host "0. 退出" -ForegroundColor Red
    Write-Host ""
}

# 清理临时文件
function Clear-TempFiles {
    Write-Host "🗑️ 清理临时文件..." -ForegroundColor Yellow
    
    $tempPaths = @(
        "$env:TEMP",
        "$env:LOCALAPPDATA\Temp",
        "C:\Windows\Temp",
        "$env:LOCALAPPDATA\Microsoft\Windows\INetCache",
        "$env:APPDATA\Local\Temp"
    )
    
    $totalCleaned = 0
    
    foreach ($path in $tempPaths) {
        if (Test-Path $path) {
            try {
                $beforeSize = (Get-ChildItem $path -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                Get-ChildItem $path -Recurse -ErrorAction SilentlyContinue | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                $afterSize = (Get-ChildItem $path -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                $cleaned = ($beforeSize - $afterSize) / 1MB
                $totalCleaned += $cleaned
                Write-Host "  ✅ $path : 清理了 $([math]::Round($cleaned, 2)) MB" -ForegroundColor Green
            }
            catch {
                Write-Host "  ⚠️ $path : 部分文件无法删除" -ForegroundColor Yellow
            }
        }
    }
    
    Write-Host "  📊 临时文件总计清理: $([math]::Round($totalCleaned, 2)) MB" -ForegroundColor Cyan
    return $totalCleaned
}

# 清理回收站
function Clear-RecycleBin {
    Write-Host "🗑️ 清理回收站..." -ForegroundColor Yellow
    try {
        Clear-RecycleBin -Force -ErrorAction Stop
        Write-Host "  ✅ 回收站已清空" -ForegroundColor Green
    }
    catch {
        Write-Host "  ⚠️ 清理回收站失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 清理浏览器缓存
function Clear-BrowserCache {
    Write-Host "🌐 清理浏览器缓存..." -ForegroundColor Yellow
    
    $browserPaths = @(
        "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Cache",
        "$env:LOCALAPPDATA\Microsoft\Edge\User Data\Default\Cache",
        "$env:APPDATA\Mozilla\Firefox\Profiles\*\cache2"
    )
    
    $totalCleaned = 0
    
    foreach ($path in $browserPaths) {
        $expandedPaths = Get-ChildItem $path -ErrorAction SilentlyContinue
        foreach ($expandedPath in $expandedPaths) {
            if (Test-Path $expandedPath.FullName) {
                try {
                    $beforeSize = (Get-ChildItem $expandedPath.FullName -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                    Get-ChildItem $expandedPath.FullName -Recurse -ErrorAction SilentlyContinue | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                    $cleaned = $beforeSize / 1MB
                    $totalCleaned += $cleaned
                    Write-Host "  ✅ 浏览器缓存: 清理了 $([math]::Round($cleaned, 2)) MB" -ForegroundColor Green
                }
                catch {
                    Write-Host "  ⚠️ 部分浏览器缓存无法删除" -ForegroundColor Yellow
                }
            }
        }
    }
    
    Write-Host "  📊 浏览器缓存总计清理: $([math]::Round($totalCleaned, 2)) MB" -ForegroundColor Cyan
}

# 清理Windows更新缓存
function Clear-WindowsUpdateCache {
    Write-Host "🔄 清理Windows更新缓存..." -ForegroundColor Yellow
    
    $updatePaths = @(
        "C:\Windows\SoftwareDistribution\Download",
        "C:\Windows\System32\catroot2"
    )
    
    foreach ($path in $updatePaths) {
        if (Test-Path $path) {
            try {
                $beforeSize = (Get-ChildItem $path -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
                Get-ChildItem $path -Recurse -ErrorAction SilentlyContinue | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                $cleaned = $beforeSize / 1MB
                Write-Host "  ✅ $path : 清理了 $([math]::Round($cleaned, 2)) MB" -ForegroundColor Green
            }
            catch {
                Write-Host "  ⚠️ $path : 需要管理员权限" -ForegroundColor Yellow
            }
        }
    }
}

# 查找大文件
function Find-LargeFiles {
    Write-Host "🔍 查找大文件 (>100MB)..." -ForegroundColor Yellow
    Write-Host "  正在扫描C盘，请稍候..." -ForegroundColor Gray
    
    try {
        $largeFiles = Get-ChildItem C:\ -Recurse -File -ErrorAction SilentlyContinue | 
                     Where-Object { $_.Length -gt 100MB } | 
                     Sort-Object Length -Descending | 
                     Select-Object -First 20
        
        if ($largeFiles) {
            Write-Host "  📁 发现的大文件:" -ForegroundColor Cyan
            foreach ($file in $largeFiles) {
                $sizeGB = [math]::Round($file.Length / 1GB, 2)
                Write-Host "    $($file.FullName) - $sizeGB GB" -ForegroundColor White
            }
        } else {
            Write-Host "  ✅ 未发现超过100MB的文件" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "  ⚠️ 扫描过程中遇到权限问题" -ForegroundColor Yellow
    }
}

# 分析磁盘使用情况
function Analyze-DiskUsage {
    Write-Host "📊 分析C盘使用情况..." -ForegroundColor Yellow
    
    $folders = Get-ChildItem C:\ -Directory -ErrorAction SilentlyContinue
    $folderSizes = @()
    
    foreach ($folder in $folders) {
        try {
            $size = (Get-ChildItem $folder.FullName -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
            if ($size -gt 0) {
                $folderSizes += [PSCustomObject]@{
                    Name = $folder.Name
                    SizeGB = [math]::Round($size / 1GB, 2)
                    Path = $folder.FullName
                }
            }
        }
        catch {
            # 忽略权限错误
        }
    }
    
    $folderSizes = $folderSizes | Sort-Object SizeGB -Descending | Select-Object -First 10
    
    Write-Host "  📁 占用空间最大的文件夹:" -ForegroundColor Cyan
    foreach ($folder in $folderSizes) {
        Write-Host "    $($folder.Name): $($folder.SizeGB) GB" -ForegroundColor White
    }
}

# 运行磁盘清理工具
function Run-DiskCleanup {
    Write-Host "🧹 启动Windows磁盘清理工具..." -ForegroundColor Yellow
    try {
        Start-Process "cleanmgr.exe" -ArgumentList "/sagerun:1" -Wait
        Write-Host "  ✅ 磁盘清理完成" -ForegroundColor Green
    }
    catch {
        Write-Host "  ⚠️ 无法启动磁盘清理工具" -ForegroundColor Red
    }
}

# 主循环
do {
    Show-CleanupMenu
    $choice = Read-Host "请输入选择 (0-9)"
    
    switch ($choice) {
        "1" { Clear-TempFiles }
        "2" { Clear-RecycleBin }
        "3" { Clear-BrowserCache }
        "4" { Clear-WindowsUpdateCache }
        "5" { 
            Write-Host "🗂️ 清理系统日志..." -ForegroundColor Yellow
            try {
                Get-EventLog -List | ForEach-Object { Clear-EventLog $_.Log -ErrorAction SilentlyContinue }
                Write-Host "  ✅ 系统日志已清理" -ForegroundColor Green
            }
            catch {
                Write-Host "  ⚠️ 需要管理员权限清理系统日志" -ForegroundColor Yellow
            }
        }
        "6" { Run-DiskCleanup }
        "7" { Find-LargeFiles }
        "8" { 
            Write-Host "🚀 执行全部清理..." -ForegroundColor Green
            Clear-TempFiles
            Clear-RecycleBin
            Clear-BrowserCache
            Clear-WindowsUpdateCache
            Run-DiskCleanup
        }
        "9" { Analyze-DiskUsage }
        "0" { 
            Write-Host "👋 退出清理工具" -ForegroundColor Green
            break
        }
        default { 
            Write-Host "❌ 无效选择，请重新输入" -ForegroundColor Red
        }
    }
    
    if ($choice -ne "0" -and $choice -ne "7" -and $choice -ne "9") {
        Write-Host ""
        Write-Host "📊 清理后空间状态:" -ForegroundColor Cyan
        $currentSpace = Get-DiskSpace
        $freedSpace = $currentSpace.FreeGB - $initialSpace.FreeGB
        if ($freedSpace -gt 0) {
            Write-Host "  🎉 释放了 $([math]::Round($freedSpace, 2)) GB 空间!" -ForegroundColor Green
        }
    }
    
    Write-Host ""
    Write-Host "按任意键继续..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Clear-Host
    
} while ($choice -ne "0")

Write-Host "🎯 清理完成！建议定期运行此脚本保持系统清洁。" -ForegroundColor Green
