# 休眠文件(hiberfil.sys)管理工具
# 可以释放 12.7GB 空间

Write-Host "💤 Windows休眠文件管理工具" -ForegroundColor Green
Write-Host "=" * 60
Write-Host ""

# 检查当前休眠状态
function Get-HibernationStatus {
    Write-Host "🔍 检查当前休眠设置..." -ForegroundColor Yellow
    
    try {
        # 检查休眠是否启用
        $hibernationEnabled = (powercfg /a | Select-String "休眠" | Measure-Object).Count -gt 0
        
        # 检查hiberfil.sys文件大小
        $hiberfilePath = "C:\hiberfil.sys"
        if (Test-Path $hiberfilePath) {
            $hiberfileSize = (Get-Item $hiberfilePath -Force).Length
            $hiberfileSizeGB = [math]::Round($hiberfileSize / 1GB, 2)
            
            Write-Host "📊 休眠文件状态:" -ForegroundColor Cyan
            Write-Host "  文件路径: $hiberfilePath" -ForegroundColor White
            Write-Host "  文件大小: $hiberfileSizeGB GB" -ForegroundColor $(if($hiberfileSizeGB -gt 5) {"Red"} else {"Yellow"})
            Write-Host "  休眠功能: $(if($hibernationEnabled) {"已启用"} else {"已禁用"})" -ForegroundColor $(if($hibernationEnabled) {"Green"} else {"Red"})
        } else {
            Write-Host "✅ 未发现休眠文件" -ForegroundColor Green
            $hiberfileSizeGB = 0
        }
        
        # 检查快速启动状态
        $fastStartup = Get-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Power" -Name "HiberbootEnabled" -ErrorAction SilentlyContinue
        $fastStartupEnabled = $fastStartup.HiberbootEnabled -eq 1
        
        Write-Host "  快速启动: $(if($fastStartupEnabled) {"已启用"} else {"已禁用"})" -ForegroundColor $(if($fastStartupEnabled) {"Yellow"} else {"Green"})
        Write-Host ""
        
        return @{
            HibernationEnabled = $hibernationEnabled
            HiberfileSizeGB = $hiberfileSizeGB
            FastStartupEnabled = $fastStartupEnabled
            HiberfilePath = $hiberfilePath
        }
    }
    catch {
        Write-Host "❌ 检查休眠状态失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 显示休眠选项菜单
function Show-HibernationMenu {
    Write-Host "💤 休眠管理选项:" -ForegroundColor Yellow
    Write-Host "1. 完全禁用休眠 (释放全部空间)" -ForegroundColor White
    Write-Host "2. 减小休眠文件大小 (释放部分空间)" -ForegroundColor White
    Write-Host "3. 仅禁用快速启动 (保留休眠功能)" -ForegroundColor White
    Write-Host "4. 重新启用休眠功能" -ForegroundColor White
    Write-Host "5. 查看详细信息和建议" -ForegroundColor Cyan
    Write-Host "0. 退出" -ForegroundColor Red
    Write-Host ""
}

# 完全禁用休眠
function Disable-Hibernation {
    Write-Host "🚫 正在完全禁用休眠功能..." -ForegroundColor Yellow
    
    try {
        # 禁用休眠
        $result = powercfg /hibernate off
        
        # 等待文件删除
        Start-Sleep -Seconds 3
        
        # 检查文件是否已删除
        if (-not (Test-Path "C:\hiberfil.sys")) {
            Write-Host "✅ 休眠功能已禁用，hiberfil.sys 文件已删除" -ForegroundColor Green
            Write-Host "🎉 成功释放约 12.7GB 空间！" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 休眠已禁用，但文件可能需要重启后才会删除" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "📝 注意事项:" -ForegroundColor Cyan
        Write-Host "  - 计算机将无法进入休眠状态" -ForegroundColor White
        Write-Host "  - 快速启动功能也会被禁用" -ForegroundColor White
        Write-Host "  - 睡眠功能仍然可用" -ForegroundColor White
        Write-Host "  - 可以随时重新启用" -ForegroundColor White
    }
    catch {
        Write-Host "❌ 禁用休眠失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 请确保以管理员权限运行此脚本" -ForegroundColor Yellow
    }
}

# 减小休眠文件大小
function Reduce-HibernationSize {
    Write-Host "📉 正在减小休眠文件大小..." -ForegroundColor Yellow
    
    try {
        # 设置休眠文件大小为50%
        $result = powercfg /hibernate /size 50
        
        Write-Host "✅ 休眠文件大小已设置为内存的50%" -ForegroundColor Green
        Write-Host "🎉 预计释放约 6-8GB 空间！" -ForegroundColor Green
        Write-Host ""
        Write-Host "📝 说明:" -ForegroundColor Cyan
        Write-Host "  - 休眠功能仍然可用" -ForegroundColor White
        Write-Host "  - 文件大小减少到原来的一半" -ForegroundColor White
        Write-Host "  - 可能需要重启才能看到效果" -ForegroundColor White
    }
    catch {
        Write-Host "❌ 减小休眠文件失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 仅禁用快速启动
function Disable-FastStartup {
    Write-Host "⚡ 正在禁用快速启动..." -ForegroundColor Yellow
    
    try {
        # 禁用快速启动
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Power" -Name "HiberbootEnabled" -Value 0
        
        Write-Host "✅ 快速启动已禁用" -ForegroundColor Green
        Write-Host "🎉 可能释放部分空间（需要重启生效）" -ForegroundColor Green
        Write-Host ""
        Write-Host "📝 说明:" -ForegroundColor Cyan
        Write-Host "  - 休眠功能仍然可用" -ForegroundColor White
        Write-Host "  - 开机速度可能稍慢" -ForegroundColor White
        Write-Host "  - 需要重启计算机才能生效" -ForegroundColor White
    }
    catch {
        Write-Host "❌ 禁用快速启动失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 重新启用休眠
function Enable-Hibernation {
    Write-Host "✅ 正在重新启用休眠功能..." -ForegroundColor Yellow
    
    try {
        # 启用休眠
        $result = powercfg /hibernate on
        
        Write-Host "✅ 休眠功能已重新启用" -ForegroundColor Green
        Write-Host ""
        Write-Host "📝 说明:" -ForegroundColor Cyan
        Write-Host "  - hiberfil.sys 文件将重新创建" -ForegroundColor White
        Write-Host "  - 文件大小约等于内存大小" -ForegroundColor White
        Write-Host "  - 快速启动功能也会启用" -ForegroundColor White
    }
    catch {
        Write-Host "❌ 启用休眠失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 显示详细信息和建议
function Show-DetailedInfo {
    Write-Host "📚 休眠功能详细说明" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "🔍 什么是 hiberfil.sys？" -ForegroundColor Yellow
    Write-Host "  - Windows休眠文件，保存内存内容到硬盘" -ForegroundColor White
    Write-Host "  - 大小通常等于物理内存大小" -ForegroundColor White
    Write-Host "  - 用于休眠和快速启动功能" -ForegroundColor White
    Write-Host ""
    
    Write-Host "💡 处理建议：" -ForegroundColor Yellow
    
    # 获取内存大小
    $memory = Get-WmiObject -Class Win32_ComputerSystem
    $memoryGB = [math]::Round($memory.TotalPhysicalMemory / 1GB, 0)
    
    Write-Host "  您的内存大小: $memoryGB GB" -ForegroundColor Cyan
    Write-Host ""
    
    if ($memoryGB -ge 16) {
        Write-Host "  🎯 推荐方案1: 完全禁用休眠" -ForegroundColor Green
        Write-Host "    - 内存充足，休眠功能使用频率低" -ForegroundColor White
        Write-Host "    - 可释放全部 12.7GB 空间" -ForegroundColor White
        Write-Host "    - 睡眠功能仍可正常使用" -ForegroundColor White
    } elseif ($memoryGB -ge 8) {
        Write-Host "  🎯 推荐方案1: 减小休眠文件" -ForegroundColor Green
        Write-Host "    - 保留休眠功能，减少空间占用" -ForegroundColor White
        Write-Host "    - 可释放约一半空间" -ForegroundColor White
        Write-Host ""
        Write-Host "  🎯 推荐方案2: 完全禁用休眠" -ForegroundColor Yellow
        Write-Host "    - 如果不使用休眠功能" -ForegroundColor White
    } else {
        Write-Host "  🎯 推荐方案: 仅禁用快速启动" -ForegroundColor Yellow
        Write-Host "    - 保留休眠功能（内存较小时有用）" -ForegroundColor White
        Write-Host "    - 释放部分空间" -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "⚠️ 注意事项：" -ForegroundColor Red
    Write-Host "  - 禁用休眠后无法使用休眠功能" -ForegroundColor White
    Write-Host "  - 快速启动被禁用，开机可能稍慢" -ForegroundColor White
    Write-Host "  - 睡眠功能不受影响" -ForegroundColor White
    Write-Host "  - 可以随时重新启用" -ForegroundColor White
    Write-Host ""
    
    Write-Host "🔄 各功能对比：" -ForegroundColor Cyan
    Write-Host "  睡眠   - 内存保持供电，快速唤醒，耗少量电" -ForegroundColor White
    Write-Host "  休眠   - 内容保存到硬盘，完全断电，启动较慢" -ForegroundColor White
    Write-Host "  关机   - 完全关闭，启动最慢，不耗电" -ForegroundColor White
}

# 检查管理员权限
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 主程序
if (-not (Test-AdminRights)) {
    Write-Host "❌ 此脚本需要管理员权限才能运行" -ForegroundColor Red
    Write-Host "💡 请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "按任意键退出..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit
}

# 获取当前状态
$status = Get-HibernationStatus

if ($status -eq $null) {
    Write-Host "❌ 无法获取休眠状态，请检查系统设置" -ForegroundColor Red
    exit
}

# 主循环
do {
    Show-HibernationMenu
    $choice = Read-Host "请选择操作 (0-5)"
    
    switch ($choice) {
        "1" { 
            Write-Host ""
            Write-Host "⚠️ 确认要完全禁用休眠功能吗？这将释放约 $($status.HiberfileSizeGB) GB 空间" -ForegroundColor Yellow
            $confirm = Read-Host "输入 Y 确认，其他键取消"
            if ($confirm -eq "Y" -or $confirm -eq "y") {
                Disable-Hibernation
            } else {
                Write-Host "❌ 操作已取消" -ForegroundColor Red
            }
        }
        "2" { 
            Write-Host ""
            Write-Host "⚠️ 确认要减小休眠文件大小吗？" -ForegroundColor Yellow
            $confirm = Read-Host "输入 Y 确认，其他键取消"
            if ($confirm -eq "Y" -or $confirm -eq "y") {
                Reduce-HibernationSize
            } else {
                Write-Host "❌ 操作已取消" -ForegroundColor Red
            }
        }
        "3" { 
            Write-Host ""
            Write-Host "⚠️ 确认要禁用快速启动吗？" -ForegroundColor Yellow
            $confirm = Read-Host "输入 Y 确认，其他键取消"
            if ($confirm -eq "Y" -or $confirm -eq "y") {
                Disable-FastStartup
            } else {
                Write-Host "❌ 操作已取消" -ForegroundColor Red
            }
        }
        "4" { 
            Write-Host ""
            Write-Host "⚠️ 确认要重新启用休眠功能吗？这将重新创建 hiberfil.sys 文件" -ForegroundColor Yellow
            $confirm = Read-Host "输入 Y 确认，其他键取消"
            if ($confirm -eq "Y" -or $confirm -eq "y") {
                Enable-Hibernation
            } else {
                Write-Host "❌ 操作已取消" -ForegroundColor Red
            }
        }
        "5" { Show-DetailedInfo }
        "0" { 
            Write-Host "👋 退出休眠管理工具" -ForegroundColor Green
            break
        }
        default { 
            Write-Host "❌ 无效选择，请重新输入" -ForegroundColor Red
        }
    }
    
    if ($choice -ne "0" -and $choice -ne "5") {
        Write-Host ""
        Write-Host "📊 更新后的状态:" -ForegroundColor Cyan
        $status = Get-HibernationStatus
    }
    
    Write-Host ""
    Write-Host "按任意键继续..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Clear-Host
    
    # 重新显示状态
    if ($choice -ne "0") {
        $status = Get-HibernationStatus
    }
    
} while ($choice -ne "0")

Write-Host "🎯 感谢使用休眠文件管理工具！" -ForegroundColor Green
