@echo off
chcp 65001 >nul
title C盘快速清理工具
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔥 C盘紧急清理工具 🔥                      ║
echo ║                  解决C盘空间不足问题                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 正在检查C盘空间状态...
for /f "tokens=3" %%a in ('dir C:\ ^| find "可用字节"') do set available=%%a
echo 📊 当前C盘可用空间: %available% 字节
echo.

echo 🚀 开始快速清理...
echo.

echo ┌─ 1. 清理临时文件
echo │
rd /s /q "%temp%" 2>nul
md "%temp%" 2>nul
rd /s /q "C:\Windows\Temp" 2>nul
md "C:\Windows\Temp" 2>nul
rd /s /q "%LOCALAPPDATA%\Temp" 2>nul
md "%LOCALAPPDATA%\Temp" 2>nul
echo │   ✅ 临时文件清理完成
echo └─

echo.
echo ┌─ 2. 清理回收站
echo │
rd /s /q "C:\$Recycle.Bin" 2>nul
echo │   ✅ 回收站清理完成
echo └─

echo.
echo ┌─ 3. 清理浏览器缓存
echo │
rd /s /q "%LOCALAPPDATA%\Google\Chrome\User Data\Default\Cache" 2>nul
rd /s /q "%LOCALAPPDATA%\Microsoft\Edge\User Data\Default\Cache" 2>nul
rd /s /q "%APPDATA%\Mozilla\Firefox\Profiles\*\cache2" 2>nul
echo │   ✅ 浏览器缓存清理完成
echo └─

echo.
echo ┌─ 4. 清理Windows更新缓存
echo │
rd /s /q "C:\Windows\SoftwareDistribution\Download" 2>nul
md "C:\Windows\SoftwareDistribution\Download" 2>nul
echo │   ✅ Windows更新缓存清理完成
echo └─

echo.
echo ┌─ 5. 清理系统缓存
echo │
del /f /s /q "C:\Windows\Prefetch\*.*" 2>nul
del /f /s /q "%LOCALAPPDATA%\Microsoft\Windows\INetCache\*.*" 2>nul
echo │   ✅ 系统缓存清理完成
echo └─

echo.
echo ┌─ 6. 清理日志文件
echo │
del /f /s /q "C:\Windows\Logs\*.*" 2>nul
del /f /s /q "C:\Windows\System32\LogFiles\*.*" 2>nul
echo │   ✅ 日志文件清理完成
echo └─

echo.
echo 🔍 再次检查C盘空间状态...
for /f "tokens=3" %%a in ('dir C:\ ^| find "可用字节"') do set available_after=%%a
echo 📊 清理后C盘可用空间: %available_after% 字节
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎉 清理完成！                          ║
echo ║                                                              ║
echo ║  💡 如果空间仍然不足，建议：                                   ║
echo ║     1. 运行 "C盘清理工具.ps1" 进行深度清理                     ║
echo ║     2. 卸载不需要的程序                                       ║
echo ║     3. 移动大文件到其他盘                                     ║
echo ║     4. 使用磁盘清理工具 (cleanmgr)                            ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 是否要运行高级清理工具？(Y/N)
set /p choice=请选择: 
if /i "%choice%"=="Y" (
    echo 🚀 启动PowerShell高级清理工具...
    powershell -ExecutionPolicy Bypass -File "C盘清理工具.ps1"
) else (
    echo 👋 清理完成，感谢使用！
)

echo.
echo 按任意键退出...
pause >nul
