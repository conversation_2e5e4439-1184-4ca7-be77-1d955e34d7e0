# Obsidian个人知识库系统实施指南

## 🚀 完整实施步骤

### 第一阶段：基础环境搭建（第1天）

#### 1.1 创建新库和文件夹结构
```bash
# 在您的工作目录创建新库
mkdir MY-KNOWLEDGE-HUB
cd MY-KNOWLEDGE-HUB

# 创建主要文件夹结构
mkdir -p "00-INBOX/Quick-Notes"
mkdir -p "00-INBOX/Web-Clips" 
mkdir -p "00-INBOX/Voice-Memos"
mkdir -p "00-INBOX/Screenshots"

mkdir -p "01-LEARNING/Courses/Programming"
mkdir -p "01-LEARNING/Courses/Technology"
mkdir -p "01-LEARNING/Courses/Personal-Dev"
mkdir -p "01-LEARNING/Books"
mkdir -p "01-LEARNING/Articles"
mkdir -p "01-LEARNING/Concepts"
mkdir -p "01-LEARNING/Learning-Maps"

mkdir -p "02-DEVELOPMENT/Projects/Active"
mkdir -p "02-DEVELOPMENT/Projects/Completed"
mkdir -p "02-DEVELOPMENT/Projects/Ideas"
mkdir -p "02-DEVELOPMENT/Code-Snippets/Python"
mkdir -p "02-DEVELOPMENT/Code-Snippets/JavaScript"
mkdir -p "02-DEVELOPMENT/Code-Snippets/Shell"
mkdir -p "02-DEVELOPMENT/Tech-Notes/Frameworks"
mkdir -p "02-DEVELOPMENT/Tech-Notes/Tools"
mkdir -p "02-DEVELOPMENT/Documentation"

mkdir -p "03-LIFE-MANAGEMENT/Goals/Annual"
mkdir -p "03-LIFE-MANAGEMENT/Goals/Quarterly"
mkdir -p "03-LIFE-MANAGEMENT/Goals/Monthly"
mkdir -p "03-LIFE-MANAGEMENT/Habits"
mkdir -p "03-LIFE-MANAGEMENT/Health"
mkdir -p "03-LIFE-MANAGEMENT/Finance"

mkdir -p "04-PERIODIC/Daily"
mkdir -p "04-PERIODIC/Weekly"
mkdir -p "04-PERIODIC/Monthly"
mkdir -p "04-PERIODIC/Reviews"

mkdir -p "05-CONNECTIONS/MOCs"
mkdir -p "05-CONNECTIONS/Index"
mkdir -p "05-CONNECTIONS/Mind-Maps"

mkdir -p "06-RESOURCES/Templates"
mkdir -p "06-RESOURCES/Attachments/Images"
mkdir -p "06-RESOURCES/Attachments/Audio"
mkdir -p "06-RESOURCES/Attachments/Video"
mkdir -p "06-RESOURCES/Tools"

mkdir -p "07-ARCHIVE"
```

#### 1.2 Obsidian基础设置
1. **打开Obsidian** → 选择"打开现有文件夹" → 选择MY-KNOWLEDGE-HUB
2. **基础设置**：
   - 文件与链接 → 新建笔记的存放位置：00-INBOX
   - 文件与链接 → 附件默认存放路径：06-RESOURCES/Attachments
   - 编辑器 → 显示行号：开启
   - 编辑器 → 自动配对括号：开启

#### 1.3 核心插件安装（第一批）
**必装插件列表：**
1. Templater
2. Dataview  
3. Calendar
4. Tag Wrangler
5. Advanced Tables

**安装步骤：**
1. 设置 → 第三方插件 → 关闭安全模式
2. 浏览 → 搜索插件名称 → 安装 → 启用

### 第二阶段：模板和工作流配置（第2天）

#### 2.1 创建模板文件
在`06-RESOURCES/Templates/`目录下创建以下模板：

**学习笔记模板（Learning-Note.md）：**
```markdown
# {{title}}

## 📚 基本信息
- **来源：** 
- **类型：** 课程/书籍/文章/视频
- **难度：** ⭐⭐⭐⭐⭐
- **状态：** #学习/进行中
- **创建时间：** {{date:YYYY-MM-DD}}

## 🎯 学习目标
- [ ] 

## 📝 核心内容

### 关键概念

### 重要知识点

### 代码示例
```language
// 代码内容
```

## 🔗 相关链接
- [[]]

## 💡 个人思考

## 📋 复习计划
- [ ] 1天后复习 {{date+1d:YYYY-MM-DD}}
- [ ] 3天后复习 {{date+3d:YYYY-MM-DD}}
- [ ] 1周后复习 {{date+1w:YYYY-MM-DD}}
- [ ] 1月后复习 {{date+1M:YYYY-MM-DD}}

---
标签: #学习 #{{date:YYYY-MM}}
```

**技术项目模板（Tech-Project.md）：**
```markdown
# {{title}}

## 📋 项目概述
**项目类型：** 
**技术栈：** 
**开始时间：** {{date:YYYY-MM-DD}}
**预计完成：** 
**状态：** #项目/进行中

## 🎯 项目目标

## 🏗️ 技术架构

## 📝 开发日志

### {{date:YYYY-MM-DD}}
- 

## 💻 核心代码

## 🐛 问题记录

| 问题 | 解决方案 | 状态 |
|------|----------|------|
|      |          |      |

## 📚 参考资料

## 🔄 下一步计划
- [ ] 

---
标签: #项目 #开发 #{{date:YYYY-MM}}
```

**日记模板（Daily-Note.md）：**
```markdown
# {{date:YYYY-MM-DD dddd}}

## 🌅 今日计划
- [ ] 

## 💻 技术学习

## 📚 知识积累

## 🎯 项目进展

## 🌱 生活记录

## 💭 今日思考

## 📊 习惯打卡
- [ ] 运动 
- [ ] 阅读
- [ ] 编码
- [ ] 反思

---
标签: #日记 #{{date:YYYY-MM}}
```

#### 2.2 Templater插件配置
1. **设置 → Templater**
2. **Template folder location:** 06-RESOURCES/Templates
3. **启用系统命令**
4. **配置快捷键：**
   - Ctrl+Shift+L → 学习笔记模板
   - Ctrl+Shift+P → 项目模板  
   - Ctrl+Shift+D → 日记模板

#### 2.3 Calendar插件配置
1. **设置 → Calendar**
2. **Daily notes folder:** 04-PERIODIC/Daily
3. **Daily note template:** 06-RESOURCES/Templates/Daily-Note.md
4. **Weekly notes folder:** 04-PERIODIC/Weekly

### 第三阶段：高级插件配置（第3-4天）

#### 3.1 学习增强插件
**安装并配置：**
1. **Spaced Repetition** - 间隔重复学习
2. **Excalidraw** - 手绘图表
3. **Mind Map** - 思维导图

#### 3.2 技术开发插件
**安装并配置：**
1. **Code Block Copy** - 代码复制
2. **Shell Commands** - 命令执行
3. **Git** - 版本控制

#### 3.3 生活管理插件
**安装并配置：**
1. **Tasks** - 任务管理
2. **Kanban** - 看板视图
3. **Tracker** - 习惯追踪

#### 3.4 多媒体插件（2025年新功能）
**安装并配置：**
1. **Image Converter** - 图片处理
2. **Audio Recorder** - 音频录制
3. **Media Extended** - 媒体播放

### 第四阶段：工作流优化（第5-7天）

#### 4.1 QuickAdd配置
创建快速操作：
1. **快速学习笔记** → 使用学习模板，保存到01-LEARNING
2. **快速项目记录** → 使用项目模板，保存到02-DEVELOPMENT
3. **快速想法记录** → 保存到00-INBOX

#### 4.2 Dataview仪表板
在根目录创建`Dashboard.md`：

```markdown
# 🏠 个人知识库仪表板

## 📊 今日概览
- 今天是：`= date(today)`
- 本周第：`= date(today).weekyear`周

## 🎯 进行中的项目
```dataview
TABLE 状态, 创建时间
FROM "02-DEVELOPMENT/Projects/Active"
WHERE contains(file.tags, "项目/进行中")
SORT file.ctime DESC
```

## 📚 最近学习
```dataview
TABLE 类型, 状态, 创建时间
FROM "01-LEARNING"
WHERE contains(file.tags, "学习")
SORT file.ctime DESC
LIMIT 5
```

## ✅ 今日任务
```dataview
TASK
FROM "04-PERIODIC/Daily"
WHERE file.name = string(date(today))
```

## 📈 本月统计
- 新建笔记：`= length(filter(file.lists.file, (f) => date(f.ctime).month = date(today).month))`篇
- 完成项目：`= length(filter(file.lists.file, (f) => contains(f.tags, "项目/已完成")))`个
```

#### 4.3 自动化脚本
在`06-RESOURCES/Tools/`创建实用脚本：

**每日整理脚本（daily-organize.js）：**
```javascript
// 自动整理INBOX中的文件
// 可以通过Shell Commands插件调用
```

### 第五阶段：同步和备份（第8天）

#### 5.1 Git版本控制设置
```bash
cd MY-KNOWLEDGE-HUB
git init
git add .
git commit -m "Initial knowledge base setup"

# 创建GitHub仓库并推送
git remote add origin https://github.com/yourusername/knowledge-hub.git
git push -u origin main
```

#### 5.2 Git插件配置
1. **设置 → Git**
2. **自动拉取间隔：** 10分钟
3. **自动推送：** 启用
4. **提交消息模板：** "Auto-sync: {{date}}"

#### 5.3 备用同步方案
配置坚果云同步MY-KNOWLEDGE-HUB文件夹

### 第六阶段：习惯养成（2-4周）

#### 6.1 每日工作流
**晨间（5分钟）：**
1. 打开Dashboard查看概览
2. 查看今日计划
3. 快速回顾昨日笔记

**工作中（随时）：**
1. 学习时使用学习笔记模板
2. 开发时记录到项目文档
3. 想法随时记录到INBOX

**晚间（10分钟）：**
1. 整理INBOX内容
2. 更新项目进展
3. 填写日记模板

#### 6.2 每周回顾
**周末（30分钟）：**
1. 回顾本周学习和项目进展
2. 整理和归档完成的内容
3. 规划下周重点

#### 6.3 每月优化
**月末（1小时）：**
1. 分析知识库使用情况
2. 优化文件夹结构
3. 更新模板和工作流
4. 备份重要数据

## 🎯 成功指标

### 第一个月目标
- [ ] 每日使用日记模板
- [ ] 完成至少5个学习笔记
- [ ] 记录至少2个技术项目
- [ ] 建立基本的知识连接

### 第三个月目标  
- [ ] 形成稳定的使用习惯
- [ ] 建立完整的知识网络
- [ ] 优化个人工作流
- [ ] 探索高级功能

---
**下一步：** 开始第一阶段的实施，有问题随时询问！
```
