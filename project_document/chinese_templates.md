# 中文版技能学习模板

## 📝 技能学习笔记模板

### 文件名：技能学习笔记模板.md
**保存位置：** 06-RESOURCES/Templates/

```markdown
# {{title}} - 技能学习

## 📊 技能信息
- **技能分类：** {{分类}}
- **难度等级：** ⭐⭐⭐⭐⭐ (1-5星)
- **当前掌握度：** {{百分比}}%
- **学习状态：** #技能/{{状态}}
- **开始时间：** {{date:YYYY-MM-DD}}
- **预计掌握：** {{目标日期}}

## 🎯 学习目标
### 本周目标
- [ ] 

### 本月目标  
- [ ] 

### 最终目标
- [ ] 

## 📚 核心内容

### 基础知识
- 

### 关键技能点
- 

### 实践要点
- 

### 常见问题
- 

## 💻 实践练习

### 练习记录
| 日期 | 练习内容 | 时长 | 完成度 | 备注 |
|------|----------|------|--------|------|
| {{date:MM-DD}} |  |  |  |  |

### 代码示例
```{{language}}
// 实践代码
```

## 🔗 技能关联
- **前置技能：** [[]]
- **相关技能：** [[]]
- **后续技能：** [[]]

## 📈 进度追踪
- **理论掌握：** {{百分比}}%
- **实践能力：** {{百分比}}%
- **综合评分：** {{百分比}}%

## 🔄 复习计划
- [ ] 3天后复习 {{date+3d:YYYY-MM-DD}}
- [ ] 1周后复习 {{date+1w:YYYY-MM-DD}}
- [ ] 2周后复习 {{date+2w:YYYY-MM-DD}}
- [ ] 1月后复习 {{date+1M:YYYY-MM-DD}}

## 📚 学习资源
- **推荐教程：** 
- **实践项目：** 
- **社区资源：** 

## 💡 学习心得
- 

## 🎯 下一步
- [ ] 

---
标签: #技能/{{分类}} #掌握度/{{等级}} #状态/{{状态}} #{{date:YYYY-MM}}
```

## 📊 技能仪表板模板

### 文件名：技能仪表板模板.md
**保存位置：** 06-RESOURCES/Templates/

```markdown
# 🎯 技能学习仪表板

## 📊 当前学习概览
**更新时间：** {{date:YYYY-MM-DD}}

### 正在学习的技能
```dataview
TABLE 
  掌握度 as "进度",
  状态 as "状态",
  file.ctime as "开始时间"
FROM "01-LEARNING/正在学习"
WHERE contains(file.tags, "技能")
SORT 掌握度 DESC
```

### 本月学习目标
- [ ] 完成 [[Python基础]] 到80%掌握度
- [ ] 开始学习 [[React框架]]
- [ ] 完成3个实践项目

### 技能分布统计
```dataview
TABLE rows.length as "技能数量"
FROM "01-LEARNING/正在学习"
WHERE contains(file.tags, "技能")
GROUP BY 技能分类
```

## 📈 学习进度分析

### 本周学习时长
| 技能 | 学习时长 | 进度提升 |
|------|----------|----------|
|      |          |          |

### 掌握度分布
- **初学者 (0-20%)：** {{数量}}个技能
- **入门 (21-40%)：** {{数量}}个技能  
- **熟练 (41-60%)：** {{数量}}个技能
- **精通 (61-80%)：** {{数量}}个技能
- **专家 (81-100%)：** {{数量}}个技能

## 🎯 近期计划

### 本周重点
1. 

### 本月目标
1. 

### 季度规划
1. 

## 🔄 需要复习的技能
```dataview
LIST
FROM "01-LEARNING"
WHERE contains(file.tags, "技能") AND 复习日期 <= date(today)
```

## 💡 学习反思
### 本周收获
- 

### 遇到的挑战
- 

### 改进计划
- 

---
标签: #仪表板 #技能管理 #{{date:YYYY-MM}}
```

## 📋 技能评估模板

### 文件名：技能评估模板.md
**保存位置：** 06-RESOURCES/Templates/

```markdown
# {{技能名称}} - 技能评估

## 📊 基本信息
- **评估日期：** {{date:YYYY-MM-DD}}
- **学习时长：** {{总时长}}
- **评估类型：** 自评

## 📈 能力评分

### 理论知识 (50%)
- **基础概念：** {{分数}}/10
- **深度理解：** {{分数}}/10
- **小计：** {{总分}}/20

### 实践能力 (50%)  
- **基本操作：** {{分数}}/10
- **问题解决：** {{分数}}/10
- **小计：** {{总分}}/20

### 总体评分
**总分：** {{总分}}/40
**掌握度：** {{百分比}}%
**等级：** {{等级}}

## 💪 优势与不足
### 主要优势
- 

### 改进空间
- 

## 📋 提升计划
### 短期计划（2周）
- [ ] 

### 中期计划（1个月）
- [ ] 

---
标签: #评估 #技能/{{技能名称}} #{{date:YYYY-MM}}
```

## 🗺️ 学习路径模板

### 文件名：学习路径模板.md
**保存位置：** 06-RESOURCES/Templates/

```markdown
# {{路径名称}} - 学习路径

## 🎯 路径概述
- **路径类型：** {{类型}}
- **难度等级：** ⭐⭐⭐⭐⭐
- **预计时长：** {{时长}}
- **适合人群：** {{人群}}
- **创建时间：** {{date:YYYY-MM-DD}}

## 🗺️ 学习阶段

### 阶段1：{{阶段名称}}
**目标：** 
**时长：** {{时长}}
**核心技能：**
- [[技能1]]
- [[技能2]]

**学习任务：**
- [ ] 
- [ ] 

### 阶段2：{{阶段名称}}
**目标：** 
**时长：** {{时长}}
**核心技能：**
- [[技能3]]
- [[技能4]]

**学习任务：**
- [ ] 
- [ ] 

### 阶段3：{{阶段名称}}
**目标：** 
**时长：** {{时长}}
**核心技能：**
- [[技能5]]
- [[技能6]]

**学习任务：**
- [ ] 
- [ ] 

## 📚 推荐资源
### 书籍
- 

### 课程
- 

### 实践项目
- 

## 🎯 学习建议
### 学习方法
- 

### 注意事项
- 

### 常见问题
- 

## 📈 进度追踪
| 阶段 | 开始时间 | 完成时间 | 状态 | 备注 |
|------|----------|----------|------|------|
| 阶段1 |  |  |  |  |
| 阶段2 |  |  |  |  |
| 阶段3 |  |  |  |  |

---
标签: #学习路径 #{{路径类型}} #{{date:YYYY-MM}}
```

## 🏷️ 中文标签系统

### 技能分类标签
```
#技能/
├── 技术/
│   ├── 编程语言/Python
│   ├── 编程语言/JavaScript
│   ├── 框架/React
│   ├── 工具/Git
│   └── 数据库/MySQL
├── 创意/
│   ├── 设计/UI设计
│   ├── 内容/写作
│   └── 营销/SEO
├── 商业/
│   ├── 管理
│   ├── 财务
│   └── 沟通
└── 生活/
    ├── 语言/英语
    ├── 健康/健身
    └── 兴趣/音乐
```

### 掌握程度标签
```
#掌握度/
├── 初学者
├── 入门
├── 熟练
├── 精通
└── 专家
```

### 学习状态标签
```
#状态/
├── 计划中
├── 学习中
├── 实践中
├── 复习中
├── 已掌握
└── 暂停
```

---

**下一步：** 开始在Obsidian中创建这些文件夹和模板文件
```
