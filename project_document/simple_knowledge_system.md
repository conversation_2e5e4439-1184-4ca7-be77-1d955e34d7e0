# 简化版个人知识库系统设计

## 📋 系统概述
**核心理念：** 输入 → 整理 → 输出  
**主要功能：** 日常信息收集 + 每日写作输出  
**设计原则：** 简单实用、快速收集、便于检索、促进创作  
**创建时间：** 2025-08-01

## 🏗️ 简化文件夹结构

```
🏠 我的知识库/
│
├── 📥 01-收集箱/                          # 快速收集区
│   ├── 今日收集/                          # 当天收集的内容
│   ├── 待整理/                            # 需要进一步整理的内容
│   └── 临时笔记/                          # 临时想法和草稿
│
├── 📚 02-知识库/                          # 已整理的知识内容
│   ├── 知识碎片/                          # 零散的知识点
│   ├── 金句收藏/                          # 精彩句子和名言
│   ├── 信息差/                            # 有价值的信息差
│   ├── 文章笔记/                          # 文章阅读笔记
│   ├── 网站资源/                          # 有用的网站链接
│   ├── 图片素材/                          # 图片和视觉素材
│   ├── 音视频/                            # 音频和视频内容
│   └── 主题合集/                          # 按主题整理的内容
│
├── ✍️ 03-写作区/                          # 写作输出区
│   ├── 每日写作/                          # 每日50字写作
│   ├── 灵感记录/                          # 写作灵感和想法
│   ├── 文章草稿/                          # 正在创作的文章
│   ├── 已发布/                            # 已完成的作品
│   └── 写作素材/                          # 写作用的素材库
│
├── 🔗 04-连接/                            # 知识连接
│   ├── 主题索引/                          # 按主题分类的索引
│   ├── 标签地图/                          # 标签关系图
│   └── 灵感触发/                          # 能触发灵感的内容组合
│
├── 📅 05-时间线/                          # 时间维度管理
│   ├── 每日回顾/                          # 每日总结
│   ├── 每周整理/                          # 每周知识整理
│   └── 月度复盘/                          # 月度回顾和规划
│
└── 🗄️ 06-归档/                            # 归档区
    ├── 过期信息/                          # 时效性过期的信息
    ├── 已用素材/                          # 已经使用过的素材
    └── 备份/                              # 重要内容备份
```

## 📝 核心模板系统

### 1. 快速收集模板
```markdown
# {{title}} - 快速收集

## 📊 基本信息
- **收集时间：** {{date:YYYY-MM-DD HH:mm}}
- **来源：** {{来源}}
- **类型：** {{知识碎片/金句/信息差/文章/网站/图片/音视频}}
- **重要程度：** ⭐⭐⭐⭐⭐ (1-5星)

## 📝 内容记录

### 核心内容
{{主要内容}}

### 关键信息
- 

### 个人思考
- 

## 🏷️ 分类标签
- **主题：** #{{主题}}
- **类型：** #{{类型}}
- **用途：** #{{用途}}

## 🔗 相关链接
- **原始链接：** {{如果有}}
- **相关笔记：** [[]]

## 💡 后续行动
- [ ] 需要深入研究
- [ ] 可用于写作
- [ ] 需要实践验证
- [ ] 分享给他人

---
收集于: {{date:YYYY-MM-DD}}
```

### 2. 每日写作模板
```markdown
# {{date:YYYY-MM-DD}} - 每日写作

## 📝 今日写作 (目标50字+)

### 写作主题
{{今日写作的主题或方向}}

### 正文内容
{{至少50字的写作内容}}

**字数统计：** {{字数}}字

## 💡 灵感来源
- **触发素材：** [[相关笔记]]
- **灵感描述：** {{什么触发了今天的写作}}

## 🎯 写作感悟
- **今日收获：** 
- **改进方向：** 
- **明日计划：** 

## 🔗 使用素材
- [[素材1]] - {{如何使用}}
- [[素材2]] - {{如何使用}}

---
标签: #每日写作 #{{date:YYYY-MM}}
```

### 3. 知识整理模板
```markdown
# {{主题}} - 知识整理

## 📊 整理信息
- **整理时间：** {{date:YYYY-MM-DD}}
- **内容数量：** {{数量}}条
- **主要来源：** {{来源}}
- **整理目的：** {{目的}}

## 📚 核心内容

### 关键知识点
1. {{知识点1}}
   - 来源：[[]]
   - 要点：
   
2. {{知识点2}}
   - 来源：[[]]
   - 要点：

### 精彩金句
> {{金句1}} —— 来源

> {{金句2}} —— 来源

### 实用信息
- {{实用信息1}}
- {{实用信息2}}

## 💡 个人思考

### 核心洞察
- 

### 应用场景
- 

### 写作角度
- 

## 🎯 行动计划
- [ ] 深入研究的方向
- [ ] 可以写作的角度
- [ ] 需要验证的观点
- [ ] 可以分享的内容

---
标签: #知识整理 #{{主题}} #{{date:YYYY-MM}}
```

### 4. 主题索引模板
```markdown
# {{主题名称}} - 主题索引

## 📊 主题概览
- **创建时间：** {{date:YYYY-MM-DD}}
- **内容数量：** {{数量}}条
- **最后更新：** {{date:YYYY-MM-DD}}

## 📚 相关内容

### 知识碎片
```dataview
LIST
FROM "02-知识库/知识碎片"
WHERE contains(file.tags, "{{主题}}")
SORT file.ctime DESC
```

### 金句收藏
```dataview
LIST
FROM "02-知识库/金句收藏"
WHERE contains(file.tags, "{{主题}}")
SORT file.ctime DESC
```

### 文章笔记
```dataview
LIST
FROM "02-知识库/文章笔记"
WHERE contains(file.tags, "{{主题}}")
SORT file.ctime DESC
```

### 相关写作
```dataview
LIST
FROM "03-写作区"
WHERE contains(file.tags, "{{主题}}")
SORT file.ctime DESC
```

## 💡 写作灵感

### 可写角度
- 

### 素材组合
- 

### 观点整合
- 

---
标签: #主题索引 #{{主题}}
```

## 🏷️ 标签系统

### 内容类型标签
```
#类型/
├── 知识碎片
├── 金句
├── 信息差
├── 文章笔记
├── 网站资源
├── 图片素材
├── 音视频
└── 写作作品
```

### 主题分类标签
```
#主题/
├── 个人成长
├── 商业思维
├── 写作技巧
├── 沟通表达
├── 科技趋势
├── 生活智慧
├── 投资理财
└── 其他
```

### 用途标签
```
#用途/
├── 写作素材
├── 学习参考
├── 灵感触发
├── 实用工具
├── 分享传播
└── 深度研究
```

### 状态标签
```
#状态/
├── 待整理
├── 已整理
├── 已使用
├── 需验证
└── 已归档
```

## 🔄 日常工作流

### 信息收集流程
1. **快速收集** → 使用快速收集模板，保存到"01-收集箱/今日收集"
2. **当日整理** → 晚上将今日收集的内容分类到对应的知识库文件夹
3. **打标签** → 为每条内容添加合适的标签
4. **建立连接** → 将相关内容通过双链连接起来

### 每日写作流程
1. **浏览素材** → 查看最近收集的内容，寻找写作灵感
2. **选择主题** → 确定今日写作的主题或角度
3. **开始写作** → 使用每日写作模板，完成至少50字
4. **记录感悟** → 记录写作过程中的思考和收获

### 每周整理流程
1. **内容回顾** → 回顾本周收集的所有内容
2. **主题整理** → 将相关内容按主题进行整理
3. **更新索引** → 更新主题索引页面
4. **规划写作** → 基于整理的内容规划下周的写作方向

## 🎯 使用场景示例

### 场景1：收集一个金句
1. 在"01-收集箱/今日收集"中创建笔记
2. 使用快速收集模板
3. 记录金句内容和来源
4. 添加标签：#类型/金句 #主题/个人成长
5. 晚上整理时移动到"02-知识库/金句收藏"

### 场景2：每日写作
1. 浏览最近收集的素材
2. 选择一个触发灵感的内容
3. 在"03-写作区/每日写作"中创建今日写作
4. 使用每日写作模板完成50字+写作
5. 记录使用的素材和写作感悟

### 场景3：主题研究
1. 在"04-连接/主题索引"中创建主题页面
2. 使用Dataview查询相关内容
3. 整理该主题下的所有素材
4. 分析可以写作的角度
5. 规划深度写作计划

---
**下一步：** 开始创建这个简化的知识库结构
```
