@echo off
chcp 65001 >nul
echo 正在创建简化版知识库...

REM 创建根目录
mkdir "我的知识库" 2>nul
cd "我的知识库"

REM 创建6个主文件夹
mkdir "01-收集箱" 2>nul
mkdir "02-知识库" 2>nul
mkdir "03-写作区" 2>nul
mkdir "04-连接" 2>nul
mkdir "05-时间线" 2>nul
mkdir "06-归档" 2>nul

REM 创建收集箱子文件夹
mkdir "01-收集箱\今日收集" 2>nul
mkdir "01-收集箱\待整理" 2>nul
mkdir "01-收集箱\临时笔记" 2>nul

REM 创建知识库子文件夹
mkdir "02-知识库\知识碎片" 2>nul
mkdir "02-知识库\金句收藏" 2>nul
mkdir "02-知识库\信息差" 2>nul
mkdir "02-知识库\文章笔记" 2>nul
mkdir "02-知识库\网站资源" 2>nul
mkdir "02-知识库\图片素材" 2>nul
mkdir "02-知识库\音视频" 2>nul
mkdir "02-知识库\主题合集" 2>nul

REM 创建写作区子文件夹
mkdir "03-写作区\每日写作" 2>nul
mkdir "03-写作区\灵感记录" 2>nul
mkdir "03-写作区\文章草稿" 2>nul
mkdir "03-写作区\已发布" 2>nul
mkdir "03-写作区\写作素材" 2>nul

REM 创建连接子文件夹
mkdir "04-连接\主题索引" 2>nul
mkdir "04-连接\标签地图" 2>nul
mkdir "04-连接\灵感触发" 2>nul

REM 创建时间线子文件夹
mkdir "05-时间线\每日回顾" 2>nul
mkdir "05-时间线\每周整理" 2>nul
mkdir "05-时间线\月度复盘" 2>nul

REM 创建归档子文件夹
mkdir "06-归档\过期信息" 2>nul
mkdir "06-归档\已用素材" 2>nul
mkdir "06-归档\备份" 2>nul

echo.
echo ✅ 简化版知识库创建完成！
echo.
echo 📊 创建统计：
echo - 主文件夹：6个
echo - 总文件夹：24个
echo - 结构层级：2层
echo.
echo 📁 创建的主文件夹：
echo   01-收集箱    （快速收集各种内容）
echo   02-知识库    （已整理的知识内容）
echo   03-写作区    （每日写作和创作）
echo   04-连接      （知识连接和索引）
echo   05-时间线    （时间维度管理）
echo   06-归档      （归档和备份）
echo.
echo 🎯 使用建议：
echo 1. 每天收集有用内容到"01-收集箱/今日收集"
echo 2. 每天在"03-写作区/每日写作"完成50字写作
echo 3. 每周将收集箱内容整理到"02-知识库"对应分类
echo 4. 使用"04-连接/主题索引"建立知识连接
echo.
echo 🚀 下一步：
echo 1. 在Obsidian中打开"我的知识库"文件夹
echo 2. 创建模板文件
echo 3. 开始收集和写作
echo.
pause
