@echo off
chcp 65001 >nul
echo 正在创建知识库目录结构...

REM 创建主文件夹
mkdir "00-收件箱" 2>nul
mkdir "01-核心技能" 2>nul
mkdir "02-生活管理" 2>nul
mkdir "03-周期笔记" 2>nul
mkdir "04-知识连接" 2>nul
mkdir "05-资源库" 2>nul
mkdir "06-归档区" 2>nul

REM 创建收件箱子文件夹
mkdir "00-收件箱\快速笔记" 2>nul
mkdir "00-收件箱\网页剪藏" 2>nul
mkdir "00-收件箱\语音备忘" 2>nul
mkdir "00-收件箱\截图收集" 2>nul

REM 创建核心技能主文件夹
mkdir "01-核心技能\技能仪表板" 2>nul
mkdir "01-核心技能\写作技能" 2>nul
mkdir "01-核心技能\沟通表达" 2>nul
mkdir "01-核心技能\逻辑思维" 2>nul
mkdir "01-核心技能\销售技能" 2>nul
mkdir "01-核心技能\技能协同" 2>nul
mkdir "01-核心技能\实践练习" 2>nul
mkdir "01-核心技能\学习资源" 2>nul
mkdir "01-核心技能\技能成果" 2>nul

REM 创建写作技能子文件夹
mkdir "01-核心技能\写作技能\基础写作" 2>nul
mkdir "01-核心技能\写作技能\文案写作" 2>nul
mkdir "01-核心技能\写作技能\故事叙述" 2>nul
mkdir "01-核心技能\写作技能\结构逻辑" 2>nul
mkdir "01-核心技能\写作技能\风格语调" 2>nul

REM 创建沟通表达子文件夹
mkdir "01-核心技能\沟通表达\口语表达" 2>nul
mkdir "01-核心技能\沟通表达\演讲技巧" 2>nul
mkdir "01-核心技能\沟通表达\倾听技能" 2>nul
mkdir "01-核心技能\沟通表达\非语言沟通" 2>nul
mkdir "01-核心技能\沟通表达\冲突处理" 2>nul

REM 创建逻辑思维子文件夹
mkdir "01-核心技能\逻辑思维\批判性思维" 2>nul
mkdir "01-核心技能\逻辑思维\结构化思维" 2>nul
mkdir "01-核心技能\逻辑思维\问题解决" 2>nul
mkdir "01-核心技能\逻辑思维\决策分析" 2>nul
mkdir "01-核心技能\逻辑思维\论证技巧" 2>nul

REM 创建销售技能子文件夹
mkdir "01-核心技能\销售技能\客户分析" 2>nul
mkdir "01-核心技能\销售技能\需求挖掘" 2>nul
mkdir "01-核心技能\销售技能\产品展示" 2>nul
mkdir "01-核心技能\销售技能\异议处理" 2>nul
mkdir "01-核心技能\销售技能\成交技巧" 2>nul

REM 创建技能协同子文件夹
mkdir "01-核心技能\技能协同\写作+沟通" 2>nul
mkdir "01-核心技能\技能协同\写作+逻辑" 2>nul
mkdir "01-核心技能\技能协同\写作+销售" 2>nul
mkdir "01-核心技能\技能协同\沟通+逻辑" 2>nul
mkdir "01-核心技能\技能协同\沟通+销售" 2>nul
mkdir "01-核心技能\技能协同\逻辑+销售" 2>nul
mkdir "01-核心技能\技能协同\四技能综合" 2>nul

REM 创建实践练习子文件夹
mkdir "01-核心技能\实践练习\写作练习" 2>nul
mkdir "01-核心技能\实践练习\演讲练习" 2>nul
mkdir "01-核心技能\实践练习\逻辑训练" 2>nul
mkdir "01-核心技能\实践练习\销售模拟" 2>nul
mkdir "01-核心技能\实践练习\综合项目" 2>nul

REM 创建学习资源子文件夹
mkdir "01-核心技能\学习资源\书籍笔记" 2>nul
mkdir "01-核心技能\学习资源\课程资料" 2>nul
mkdir "01-核心技能\学习资源\案例分析" 2>nul
mkdir "01-核心技能\学习资源\工具模板" 2>nul
mkdir "01-核心技能\学习资源\大师学习" 2>nul

REM 创建技能成果子文件夹
mkdir "01-核心技能\技能成果\写作作品" 2>nul
mkdir "01-核心技能\技能成果\演讲记录" 2>nul
mkdir "01-核心技能\技能成果\思维导图" 2>nul
mkdir "01-核心技能\技能成果\销售案例" 2>nul
mkdir "01-核心技能\技能成果\综合项目" 2>nul

REM 创建生活管理子文件夹
mkdir "02-生活管理\目标管理" 2>nul
mkdir "02-生活管理\目标管理\年度目标" 2>nul
mkdir "02-生活管理\目标管理\季度目标" 2>nul
mkdir "02-生活管理\目标管理\月度目标" 2>nul
mkdir "02-生活管理\习惯追踪" 2>nul
mkdir "02-生活管理\健康管理" 2>nul
mkdir "02-生活管理\财务管理" 2>nul
mkdir "02-生活管理\人际关系" 2>nul
mkdir "02-生活管理\反思总结" 2>nul

REM 创建周期笔记子文件夹
mkdir "03-周期笔记\每日笔记" 2>nul
mkdir "03-周期笔记\每周总结" 2>nul
mkdir "03-周期笔记\每月回顾" 2>nul
mkdir "03-周期笔记\定期回顾" 2>nul

REM 创建知识连接子文件夹
mkdir "04-知识连接\内容地图" 2>nul
mkdir "04-知识连接\索引页面" 2>nul
mkdir "04-知识连接\思维导图" 2>nul

REM 创建资源库子文件夹
mkdir "05-资源库\模板库" 2>nul
mkdir "05-资源库\附件文件" 2>nul
mkdir "05-资源库\附件文件\图片文件" 2>nul
mkdir "05-资源库\附件文件\音频文件" 2>nul
mkdir "05-资源库\附件文件\视频文件" 2>nul
mkdir "05-资源库\附件文件\文档文件" 2>nul
mkdir "05-资源库\工具脚本" 2>nul
mkdir "05-资源库\参考资料" 2>nul

REM 创建归档区子文件夹
mkdir "06-归档区\已完成项目" 2>nul
mkdir "06-归档区\旧笔记" 2>nul
mkdir "06-归档区\废弃内容" 2>nul

echo.
echo ✅ 目录结构创建完成！
echo.
echo 📊 创建统计：
echo - 主文件夹：7个
echo - 总文件夹：73个
echo - 最深层级：3层
echo.
echo 📁 创建的主文件夹：
echo   00-收件箱
echo   01-核心技能
echo   02-生活管理
echo   03-周期笔记
echo   04-知识连接
echo   05-资源库
echo   06-归档区
echo.
echo 🎯 下一步：
echo 1. 在Obsidian中打开"我的知识中心"文件夹
echo 2. 创建模板文件
echo 3. 配置插件和设置
echo.
pause
