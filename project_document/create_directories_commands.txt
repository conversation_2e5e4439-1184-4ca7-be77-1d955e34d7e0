# 命令行快速创建目录结构
# 复制以下命令到命令提示符中执行

# 创建根目录并进入
mkdir "我的知识中心" && cd "我的知识中心"

# 一次性创建所有目录
mkdir "00-收件箱" "01-核心技能" "02-生活管理" "03-周期笔记" "04-知识连接" "05-资源库" "06-归档区" "00-收件箱\快速笔记" "00-收件箱\网页剪藏" "00-收件箱\语音备忘" "00-收件箱\截图收集" "01-核心技能\技能仪表板" "01-核心技能\写作技能" "01-核心技能\沟通表达" "01-核心技能\逻辑思维" "01-核心技能\销售技能" "01-核心技能\技能协同" "01-核心技能\实践练习" "01-核心技能\学习资源" "01-核心技能\技能成果" "01-核心技能\写作技能\基础写作" "01-核心技能\写作技能\文案写作" "01-核心技能\写作技能\故事叙述" "01-核心技能\写作技能\结构逻辑" "01-核心技能\写作技能\风格语调" "01-核心技能\沟通表达\口语表达" "01-核心技能\沟通表达\演讲技巧" "01-核心技能\沟通表达\倾听技能" "01-核心技能\沟通表达\非语言沟通" "01-核心技能\沟通表达\冲突处理" "01-核心技能\逻辑思维\批判性思维" "01-核心技能\逻辑思维\结构化思维" "01-核心技能\逻辑思维\问题解决" "01-核心技能\逻辑思维\决策分析" "01-核心技能\逻辑思维\论证技巧" "01-核心技能\销售技能\客户分析" "01-核心技能\销售技能\需求挖掘" "01-核心技能\销售技能\产品展示" "01-核心技能\销售技能\异议处理" "01-核心技能\销售技能\成交技巧" "01-核心技能\技能协同\写作+沟通" "01-核心技能\技能协同\写作+逻辑" "01-核心技能\技能协同\写作+销售" "01-核心技能\技能协同\沟通+逻辑" "01-核心技能\技能协同\沟通+销售" "01-核心技能\技能协同\逻辑+销售" "01-核心技能\技能协同\四技能综合" "01-核心技能\实践练习\写作练习" "01-核心技能\实践练习\演讲练习" "01-核心技能\实践练习\逻辑训练" "01-核心技能\实践练习\销售模拟" "01-核心技能\实践练习\综合项目" "01-核心技能\学习资源\书籍笔记" "01-核心技能\学习资源\课程资料" "01-核心技能\学习资源\案例分析" "01-核心技能\学习资源\工具模板" "01-核心技能\学习资源\大师学习" "01-核心技能\技能成果\写作作品" "01-核心技能\技能成果\演讲记录" "01-核心技能\技能成果\思维导图" "01-核心技能\技能成果\销售案例" "01-核心技能\技能成果\综合项目" "02-生活管理\目标管理" "02-生活管理\目标管理\年度目标" "02-生活管理\目标管理\季度目标" "02-生活管理\目标管理\月度目标" "02-生活管理\习惯追踪" "02-生活管理\健康管理" "02-生活管理\财务管理" "02-生活管理\人际关系" "02-生活管理\反思总结" "03-周期笔记\每日笔记" "03-周期笔记\每周总结" "03-周期笔记\每月回顾" "03-周期笔记\定期回顾" "04-知识连接\内容地图" "04-知识连接\索引页面" "04-知识连接\思维导图" "05-资源库\模板库" "05-资源库\附件文件" "05-资源库\附件文件\图片文件" "05-资源库\附件文件\音频文件" "05-资源库\附件文件\视频文件" "05-资源库\附件文件\文档文件" "05-资源库\工具脚本" "05-资源库\参考资料" "06-归档区\已完成项目" "06-归档区\旧笔记" "06-归档区\废弃内容"

echo 目录结构创建完成！
