# 磁盘空间分析工具
# 帮助找出占用空间最大的文件和文件夹

param(
    [string]$Path = "C:\",
    [int]$TopCount = 20,
    [int]$MinSizeMB = 10
)

Write-Host "🔍 磁盘空间分析工具" -ForegroundColor Green
Write-Host "=" * 60
Write-Host "📁 分析路径: $Path" -ForegroundColor Cyan
Write-Host "📊 显示前 $TopCount 个最大项目" -ForegroundColor Cyan
Write-Host "📏 最小大小: $MinSizeMB MB" -ForegroundColor Cyan
Write-Host ""

# 获取磁盘信息
function Get-DiskInfo {
    param([string]$DriveLetter)
    
    $drive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$DriveLetter'"
    if ($drive) {
        $totalGB = [math]::Round($drive.Size / 1GB, 2)
        $freeGB = [math]::Round($drive.FreeSpace / 1GB, 2)
        $usedGB = [math]::Round(($drive.Size - $drive.FreeSpace) / 1GB, 2)
        $freePercent = [math]::Round(($drive.FreeSpace / $drive.Size) * 100, 2)
        
        Write-Host "💾 $DriveLetter 盘信息:" -ForegroundColor Yellow
        Write-Host "  总容量: $totalGB GB" -ForegroundColor White
        Write-Host "  已使用: $usedGB GB" -ForegroundColor White
        Write-Host "  可用空间: $freeGB GB ($freePercent%)" -ForegroundColor $(if($freePercent -lt 10) {"Red"} elseif($freePercent -lt 20) {"Yellow"} else {"Green"})
        Write-Host ""
    }
}

# 分析文件夹大小
function Get-FolderSize {
    param(
        [string]$FolderPath,
        [int]$TopN = 10
    )
    
    Write-Host "📁 分析文件夹大小..." -ForegroundColor Yellow
    Write-Host "  正在扫描: $FolderPath" -ForegroundColor Gray
    
    $folders = @()
    
    try {
        $subFolders = Get-ChildItem $FolderPath -Directory -ErrorAction SilentlyContinue
        
        foreach ($folder in $subFolders) {
            Write-Progress -Activity "扫描文件夹" -Status "正在分析: $($folder.Name)" -PercentComplete (($folders.Count / $subFolders.Count) * 100)
            
            try {
                $size = (Get-ChildItem $folder.FullName -Recurse -File -ErrorAction SilentlyContinue | 
                        Measure-Object -Property Length -Sum).Sum
                
                if ($size -gt ($MinSizeMB * 1MB)) {
                    $folders += [PSCustomObject]@{
                        Name = $folder.Name
                        Path = $folder.FullName
                        SizeMB = [math]::Round($size / 1MB, 2)
                        SizeGB = [math]::Round($size / 1GB, 2)
                        FileCount = (Get-ChildItem $folder.FullName -Recurse -File -ErrorAction SilentlyContinue).Count
                    }
                }
            }
            catch {
                # 忽略权限错误
            }
        }
        
        Write-Progress -Activity "扫描文件夹" -Completed
        
        $topFolders = $folders | Sort-Object SizeMB -Descending | Select-Object -First $TopN
        
        if ($topFolders) {
            Write-Host "  📊 占用空间最大的文件夹:" -ForegroundColor Cyan
            Write-Host "  " + ("-" * 80) -ForegroundColor Gray
            Write-Host "  {0,-40} {1,10} {2,10} {3,10}" -f "文件夹名称", "大小(MB)", "大小(GB)", "文件数" -ForegroundColor White
            Write-Host "  " + ("-" * 80) -ForegroundColor Gray
            
            foreach ($folder in $topFolders) {
                $color = if ($folder.SizeGB -gt 5) { "Red" } elseif ($folder.SizeGB -gt 1) { "Yellow" } else { "White" }
                Write-Host ("  {0,-40} {1,10} {2,10} {3,10}" -f 
                    ($folder.Name.Substring(0, [Math]::Min(40, $folder.Name.Length))),
                    $folder.SizeMB,
                    $folder.SizeGB,
                    $folder.FileCount) -ForegroundColor $color
            }
        } else {
            Write-Host "  ✅ 未发现超过 $MinSizeMB MB 的文件夹" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "  ❌ 扫描失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
    return $folders
}

# 查找大文件
function Find-LargeFiles {
    param(
        [string]$SearchPath,
        [int]$MinSizeMB = 100,
        [int]$TopN = 20
    )
    
    Write-Host "🔍 查找大文件 (>$MinSizeMB MB)..." -ForegroundColor Yellow
    Write-Host "  正在扫描: $SearchPath" -ForegroundColor Gray
    
    try {
        $largeFiles = Get-ChildItem $SearchPath -Recurse -File -ErrorAction SilentlyContinue | 
                     Where-Object { $_.Length -gt ($MinSizeMB * 1MB) } | 
                     Sort-Object Length -Descending | 
                     Select-Object -First $TopN
        
        if ($largeFiles) {
            Write-Host "  📊 发现的大文件:" -ForegroundColor Cyan
            Write-Host "  " + ("-" * 100) -ForegroundColor Gray
            Write-Host "  {0,-60} {1,15} {2,15}" -f "文件路径", "大小(MB)", "大小(GB)" -ForegroundColor White
            Write-Host "  " + ("-" * 100) -ForegroundColor Gray
            
            foreach ($file in $largeFiles) {
                $sizeMB = [math]::Round($file.Length / 1MB, 2)
                $sizeGB = [math]::Round($file.Length / 1GB, 2)
                $color = if ($sizeGB -gt 2) { "Red" } elseif ($sizeGB -gt 0.5) { "Yellow" } else { "White" }
                
                $displayPath = $file.FullName
                if ($displayPath.Length -gt 60) {
                    $displayPath = "..." + $displayPath.Substring($displayPath.Length - 57)
                }
                
                Write-Host ("  {0,-60} {1,15} {2,15}" -f $displayPath, $sizeMB, $sizeGB) -ForegroundColor $color
            }
        } else {
            Write-Host "  ✅ 未发现超过 $MinSizeMB MB 的文件" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "  ❌ 扫描失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

# 分析文件类型分布
function Analyze-FileTypes {
    param(
        [string]$SearchPath,
        [int]$TopN = 15
    )
    
    Write-Host "📋 分析文件类型分布..." -ForegroundColor Yellow
    Write-Host "  正在扫描: $SearchPath" -ForegroundColor Gray
    
    try {
        $fileTypes = @{}
        $files = Get-ChildItem $SearchPath -Recurse -File -ErrorAction SilentlyContinue
        
        foreach ($file in $files) {
            $extension = $file.Extension.ToLower()
            if ([string]::IsNullOrEmpty($extension)) {
                $extension = "(无扩展名)"
            }
            
            if ($fileTypes.ContainsKey($extension)) {
                $fileTypes[$extension].Count++
                $fileTypes[$extension].TotalSize += $file.Length
            } else {
                $fileTypes[$extension] = @{
                    Count = 1
                    TotalSize = $file.Length
                }
            }
        }
        
        $sortedTypes = $fileTypes.GetEnumerator() | 
                      Sort-Object { $_.Value.TotalSize } -Descending | 
                      Select-Object -First $TopN
        
        if ($sortedTypes) {
            Write-Host "  📊 文件类型分布 (按大小排序):" -ForegroundColor Cyan
            Write-Host "  " + ("-" * 70) -ForegroundColor Gray
            Write-Host "  {0,-15} {1,10} {2,15} {3,15}" -f "文件类型", "数量", "总大小(MB)", "总大小(GB)" -ForegroundColor White
            Write-Host "  " + ("-" * 70) -ForegroundColor Gray
            
            foreach ($type in $sortedTypes) {
                $sizeMB = [math]::Round($type.Value.TotalSize / 1MB, 2)
                $sizeGB = [math]::Round($type.Value.TotalSize / 1GB, 2)
                $color = if ($sizeGB -gt 1) { "Red" } elseif ($sizeMB -gt 100) { "Yellow" } else { "White" }
                
                Write-Host ("  {0,-15} {1,10} {2,15} {3,15}" -f 
                    $type.Key, 
                    $type.Value.Count, 
                    $sizeMB, 
                    $sizeGB) -ForegroundColor $color
            }
        }
    }
    catch {
        Write-Host "  ❌ 分析失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
}

# 生成清理建议
function Generate-CleanupSuggestions {
    param($FolderAnalysis)
    
    Write-Host "💡 清理建议:" -ForegroundColor Green
    Write-Host ""
    
    # 常见的可清理文件夹
    $cleanupTargets = @{
        "Temp" = "临时文件，可以安全删除"
        "Cache" = "缓存文件，可以删除"
        "Logs" = "日志文件，旧的可以删除"
        "Downloads" = "下载文件，检查是否需要"
        "Desktop" = "桌面文件，整理到其他位置"
        "Documents" = "文档文件，移动到其他盘"
        "Pictures" = "图片文件，移动到其他盘"
        "Videos" = "视频文件，移动到其他盘"
        "Music" = "音乐文件，移动到其他盘"
    }
    
    $suggestions = @()
    
    foreach ($folder in $FolderAnalysis) {
        foreach ($target in $cleanupTargets.Keys) {
            if ($folder.Name -like "*$target*") {
                $suggestions += "🗂️ $($folder.Name) ($($folder.SizeGB) GB) - $($cleanupTargets[$target])"
            }
        }
    }
    
    if ($suggestions) {
        foreach ($suggestion in $suggestions) {
            Write-Host "  $suggestion" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ✅ 未发现明显的清理目标" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "🔧 通用清理建议:" -ForegroundColor Cyan
    Write-Host "  1. 运行磁盘清理工具 (cleanmgr)" -ForegroundColor White
    Write-Host "  2. 清理浏览器缓存和下载" -ForegroundColor White
    Write-Host "  3. 卸载不需要的程序" -ForegroundColor White
    Write-Host "  4. 移动大文件到其他盘" -ForegroundColor White
    Write-Host "  5. 清空回收站" -ForegroundColor White
    Write-Host "  6. 删除旧的系统还原点" -ForegroundColor White
    Write-Host ""
}

# 主程序
try {
    # 显示磁盘信息
    $driveLetter = $Path.Substring(0, 2)
    Get-DiskInfo $driveLetter
    
    # 分析文件夹
    $folderAnalysis = Get-FolderSize $Path $TopCount
    
    # 查找大文件
    Find-LargeFiles $Path 100 $TopCount
    
    # 分析文件类型
    Analyze-FileTypes $Path 15
    
    # 生成清理建议
    Generate-CleanupSuggestions $folderAnalysis
    
    Write-Host "✅ 分析完成！" -ForegroundColor Green
    Write-Host ""
    Write-Host "💾 建议使用 '快速清理C盘.bat' 或 'C盘清理工具.ps1' 进行清理" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ 分析过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
